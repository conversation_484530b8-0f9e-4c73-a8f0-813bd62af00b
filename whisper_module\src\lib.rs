use std::path::PathBuf;
use std::process::Stdio;
use tokio::process::Command as TokioCommand;
use tokio::sync::mpsc::{self, Receiver};
use tokio::io::{AsyncBufReadExt, BufReader as AsyncBufReader};
use tokio::task;

#[derive(Debug, <PERSON>lone)]
pub struct WhisperConfig {
    pub model_path: PathBuf,
    pub step_ms: u32,      // e.g. 500
    pub length_ms: u32,    // e.g. 5000
    pub threads: u32,      // e.g. 4
    pub language: String,  // e.g. "en"
    pub whisper_bin: PathBuf, // path to whisper-stream binary
}

impl Default for WhisperConfig {
    fn default() -> Self {
        Self {
            model_path: PathBuf::from("whisper.cpp/models/ggml-base.en.bin"),
            step_ms: 800,   // Optimized for responsiveness
            length_ms: 2500, // Balanced for accuracy and speed
            threads: 4,
            language: "en".to_string(),
            whisper_bin: PathBuf::from("whisper.cpp/build/bin/Release/whisper-stream.exe"),
        }
    }
}

#[derive(Debug)]
pub enum WhisperError {
    Io(std::io::Error),
    Process(String),
    StreamNotFound,
}

impl From<std::io::Error> for WhisperError {
    fn from(e: std::io::Error) -> Self {
        WhisperError::Io(e)
    }
}

/// Starts whisper-stream and returns a channel receiver for real-time transcriptions.
pub async fn start_streaming(config: WhisperConfig) -> Result<Receiver<String>, WhisperError> {
    // Check if binary exists
    if !config.whisper_bin.exists() {
        return Err(WhisperError::StreamNotFound);
    }
    // Build command
    let mut cmd = TokioCommand::new(&config.whisper_bin);
    cmd.arg("-m").arg(&config.model_path)
        .arg("-t").arg(config.threads.to_string())
        .arg("--step").arg(config.step_ms.to_string())
        .arg("--length").arg(config.length_ms.to_string())
        .arg("-l").arg(&config.language)
        .arg("--vad-thold").arg("0.01")  // Maximum sensitivity
        .arg("--freq-thold").arg("5.0")  // Minimum frequency threshold
        .arg("--keep").arg("5000")       // Keep maximum context
        .arg("--max-tokens").arg("16")   // Very fast processing
        .arg("--audio-ctx").arg("256")   // Smaller context for speed
        .arg("-c").arg("0")              // Use capture device 0
        .arg("--keep-context")           // Keep context between chunks
        .arg("--print-special")          // Print special tokens for debugging
        .stdout(Stdio::piped())
        .stderr(Stdio::piped());
    println!("🎤 Starting Whisper with command: {:?}", cmd);
    let mut child = cmd.spawn().map_err(|e| {
        eprintln!("❌ Failed to spawn Whisper process: {:?}", e);
        e
    })?;

    let stdout = child.stdout.take().ok_or_else(|| WhisperError::Process("Failed to capture stdout".to_string()))?;
    let stderr = child.stderr.take().ok_or_else(|| WhisperError::Process("Failed to capture stderr".to_string()))?;
    let reader = AsyncBufReader::new(stdout);
    let error_reader = AsyncBufReader::new(stderr);
    let mut lines = reader.lines();
    let mut error_lines = error_reader.lines();
    let (tx, rx) = mpsc::channel(32);

    // Spawn a task to read stderr for debugging
    task::spawn(async move {
        while let Ok(Some(line)) = error_lines.next_line().await {
            if !line.trim().is_empty() {
                eprintln!("🎤 Whisper stderr: {}", line);
            }
        }
    });

    // Spawn a task to read lines and send to channel
    let tx_clone = tx.clone();
    task::spawn(async move {
        println!("🎤 Whisper stdout reader started");
        while let Ok(Some(line)) = lines.next_line().await {
            println!("🎤 Raw Whisper output: '{}'", line);
            let trimmed = line.trim();
            if !trimmed.is_empty() &&
               !trimmed.contains("[Start speaking]") &&
               !trimmed.contains("[BLANK_AUDIO]") &&
               !trimmed.contains("whisper_print_timings") &&
               !trimmed.starts_with("whisper_") {
                println!("🗣️ Sending voice input: '{}'", trimmed);
                let _ = tx_clone.send(trimmed.to_string()).await;
            }
        }
        println!("🎤 Whisper stdout reader ended");
    });

    Ok(rx)
}
